# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/roomradar

# JWT Configuration
JWT_SECRET=roomradar_super_secret_jwt_key_2024_development
JWT_EXPIRE=7d

# Client Configuration
CLIENT_URL=http://localhost:3000

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=
EMAIL_PASS=

# Cloud Storage Configuration (Cloudinary)
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=

# Payment Gateway (Stripe/Razorpay)
STRIPE_SECRET_KEY=
STRIPE_PUBLISHABLE_KEY=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
